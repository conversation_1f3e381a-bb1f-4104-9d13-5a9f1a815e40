"""
Tests for the train endpoint.
"""

import io
import json

import pytest
from fastapi import status
from fastapi.testclient import TestClient

# Import the app
from api.main import app
from database.services.model_metadata_service import ModelMetadataError
from database.services.training_data_service import TrainingDataError
from src.config.paths import get_run_paths
from tests.api.routes.test_helpers import (
    get_mock_training_data,
    get_test_data,
    verify_train_response,
)
from tests.fixtures.data import get_sample_model_run_data, get_sample_model_version_data


@pytest.fixture(name="client")
def fixture_client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


def test_train_endpoint_success(client, monkeypatch):
    """
    Test the train endpoint with valid data.

    This test verifies that the train endpoint returns a 200 OK response
    when provided with valid data and the database queries succeed.
    """

    # Mock the dependencies
    def mock_get_profile():
        return "development"

    async def mock_get_training_data(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        return get_mock_training_data()

    # Apply the mocks
    monkeypatch.setattr("api.utils.get_supabase_profile", mock_get_profile)
    monkeypatch.setattr(
        "database.services.training_data_service.TrainingDataService.get_training_data",
        mock_get_training_data,
    )

    # Define test data
    payload = {"model_run_uuid": "f72599b5-942e-4877-bbea-1717244908da"}

    # Make request
    response = client.post(
        "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
    )

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    # Verify the response using the helper function
    verify_train_response(response_data)


def test_train_endpoint_model_not_found(client, monkeypatch):
    """
    Test the train endpoint when the model version is not found.

    This test verifies that the train endpoint returns a 404 Not Found response
    when the specified model version does not exist.
    """

    # Mock the dependencies
    def mock_get_profile():
        return "development"

    async def mock_get_model_metadata(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        raise ModelMetadataError("Model run not found with UUID: nonexistent-uuid")

    # Apply the mocks
    monkeypatch.setattr("api.utils.get_supabase_profile", mock_get_profile)
    monkeypatch.setattr(
        "database.services.model_metadata_service.ModelMetadataService.get_model_metadata",
        mock_get_model_metadata,
    )

    # Define test data
    payload = {"model_run_uuid": "nonexistent-uuid"}

    # Make request
    response = client.post(
        "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
    )

    # Verify response
    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert "not found" in response_data["detail"].lower()
    assert "nonexistent-uuid" in response_data["detail"]


def test_train_endpoint_dataset_not_found(client, monkeypatch):
    """
    Test the train endpoint when the dataset is not found.

    This test verifies that the train endpoint returns a 404 Not Found response
    when the specified dataset does not exist.
    """

    # Mock the dependencies
    def mock_get_profile():
        return "development"

    async def mock_get_model_metadata(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        # Get sample data from fixtures
        sample_model_version = get_sample_model_version_data()
        sample_model_run = get_sample_model_run_data()
        return {
            "model": {"uuid": "model-123", "name": "ResNet50"},
            "model_version": sample_model_version,
            "model_run": sample_model_run,
        }

    async def mock_get_training_data(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        raise TrainingDataError("Dataset not found with UUID: nonexistent-uuid")

    # Apply the mocks
    monkeypatch.setattr("api.utils.get_supabase_profile", mock_get_profile)
    monkeypatch.setattr(
        "database.services.model_metadata_service.ModelMetadataService.get_model_metadata",
        mock_get_model_metadata,
    )
    monkeypatch.setattr(
        "database.services.training_data_service.TrainingDataService.get_training_data",
        mock_get_training_data,
    )

    # Define test data
    payload = {"model_run_uuid": "f72599b5-942e-4877-bbea-1717244908da"}

    # Make request
    response = client.post(
        "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
    )

    # Verify response
    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert (
        "dataset not found" in response_data["detail"].lower()
        or "not found" in response_data["detail"].lower()
    )


def test_train_endpoint_invalid_request(client):
    """
    Test the train endpoint with invalid request data.

    This test verifies that the train endpoint returns a 422 Unprocessable Entity
    response when the request data is invalid.
    """
    # Define test data with missing required fields
    payload = {
        # Missing model_run_uuid
    }

    # Make request
    response = client.post(
        "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
    )

    # Verify response
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_data = response.json()
    assert (
        "model_run_uuid" in str(response_data).lower()
    )  # Field name should be in the error


def test_train_endpoint_database_error(client, monkeypatch):
    """
    Test the train endpoint when a database error occurs.

    This test verifies that the train endpoint returns a 422 Unprocessable Entity
    response when a database error occurs.
    """

    # Mock the dependencies
    def mock_get_profile():
        return "development"

    async def mock_get_model_metadata(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        raise ConnectionError("Database connection failed")

    # Apply the mocks
    monkeypatch.setattr("api.utils.get_supabase_profile", mock_get_profile)
    monkeypatch.setattr(
        "database.services.model_metadata_service.ModelMetadataService.get_model_metadata",
        mock_get_model_metadata,
    )

    # Define test data
    payload = {"model_run_uuid": "f72599b5-942e-4877-bbea-1717244908da"}

    # Make request
    response = client.post(
        "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
    )

    # Verify response
    assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
    response_data = response.json()
    assert "detail" in response_data
    assert "database connection failed" in response_data["detail"].lower()


def test_train_stats_endpoint_success(client, monkeypatch):
    """
    Test the /api/v1/train/{model_run_uuid}/stats endpoint for a successful response using mocks.
    """
    # Get sample data for the test
    _, _, sample_model_run = get_test_data()
    model_run_uuid = sample_model_run["uuid"]

    # Create mock metrics data
    summary_metrics = {
        "final": {"train_loss": 0.1, "train_accuracy": 0.95},
        "timing": {"total_training_time": 16.4},
        "resources": {"cpu_percent_max": 37.1},
    }

    history_metrics = {
        "batch_metrics": [
            {"epoch": 0, "batch": 0, "loss": 0.1, "accuracy": 0.95, "time": 0.65}
        ]
    }

    # Use helper functions to get the paths
    run_paths = get_run_paths(model_run_uuid)
    summary_path = run_paths.metrics_summary
    history_path = run_paths.metrics_history

    def mock_exists(path):
        # Match exact paths for the metrics files
        if path in (summary_path, history_path):
            return True
        return False

    # pylint: disable=unused-argument
    def mock_open(path, *args, **kwargs):
        if path == summary_path:
            return io.StringIO(json.dumps(summary_metrics))
        if path == history_path:
            return io.StringIO(json.dumps(history_metrics))
        raise FileNotFoundError

    # Apply mocks for Path methods
    monkeypatch.setattr("pathlib.Path.exists", mock_exists)
    monkeypatch.setattr("builtins.open", mock_open)

    # Make request
    response = client.get(f"/api/v1/train/train/{model_run_uuid}/stats")

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    # Verify response structure
    assert "summary" in data
    assert "history" in data

    # Verify summary metrics
    assert (
        data["summary"]["final"]["train_loss"] == summary_metrics["final"]["train_loss"]
    )
    assert (
        data["summary"]["final"]["train_accuracy"]
        == summary_metrics["final"]["train_accuracy"]
    )
    assert (
        data["summary"]["timing"]["total_training_time"]
        == summary_metrics["timing"]["total_training_time"]
    )
    assert (
        data["summary"]["resources"]["cpu_percent_max"]
        == summary_metrics["resources"]["cpu_percent_max"]
    )

    # Verify history metrics
    assert len(data["history"]["batch_metrics"]) == len(
        history_metrics["batch_metrics"]
    )
    assert (
        data["history"]["batch_metrics"][0]["epoch"]
        == history_metrics["batch_metrics"][0]["epoch"]
    )
    assert (
        data["history"]["batch_metrics"][0]["batch"]
        == history_metrics["batch_metrics"][0]["batch"]
    )
    assert (
        data["history"]["batch_metrics"][0]["loss"]
        == history_metrics["batch_metrics"][0]["loss"]
    )


def test_train_stats_endpoint_not_found(client, monkeypatch):
    """
    Test the /api/v1/train/{model_run_uuid}/stats endpoint for a missing run using mocks.
    """
    # Get sample data for the test
    _, _, sample_model_run = get_test_data()
    model_run_uuid = sample_model_run["uuid"]

    # Apply mock to simulate missing files
    # This will make the endpoint return a 404 with the correct error message
    # pylint: disable=unused-argument
    def mock_exists(path):
        # Return False for any path to simulate missing files
        return False

    monkeypatch.setattr("os.path.exists", mock_exists)

    # Make request
    response = client.get(f"/api/v1/train/train/{model_run_uuid}/stats")

    # Verify response
    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert "detail" in response_data
    assert "not found" in response_data["detail"].lower()
    assert model_run_uuid in response_data["detail"]


def test_train_logs_endpoint_success(client, monkeypatch):
    """
    Test the /api/v1/train/{model_run_uuid}/logs endpoint for a successful response using mocks.
    """
    # Get sample data for the test
    _, _, sample_model_run = get_test_data()
    model_run_uuid = sample_model_run["uuid"]

    # Define test data
    log_file_name = "training.log"
    log_content = (
        "2025-06-23 16:38:52 - INFO - Starting training for model run: {model_run_uuid}\n"
        "2025-06-23 16:38:55 - INFO - Epoch 1/10 started\n"
        "2025-06-23 16:39:12 - INFO - Epoch 1/10 completed, loss: 0.723, accuracy: 0.512"
    )

    # Define paths for exact matching using centralized functions
    run_paths = get_run_paths(model_run_uuid)
    logs_dir = run_paths.logs
    log_file_path = logs_dir / log_file_name

    # Define mock functions
    def mock_is_dir(path):
        # Only return True for the exact logs directory path
        return path == logs_dir

    def mock_iterdir(path):
        # Only return log files for the exact logs directory
        if path == logs_dir:
            return [log_file_path]
        return []

    def mock_is_file(path):
        # Only return True for the exact log file path
        return path == log_file_path

    # pylint: disable=unused-argument
    def mock_open(path, *args, **kwargs):
        # Only return content for the exact log file path
        if path == log_file_path:
            return io.StringIO(log_content)
        raise FileNotFoundError

    # Apply mocks for Path methods
    monkeypatch.setattr("pathlib.Path.is_dir", mock_is_dir)
    monkeypatch.setattr("pathlib.Path.iterdir", mock_iterdir)
    monkeypatch.setattr("pathlib.Path.is_file", mock_is_file)
    monkeypatch.setattr("builtins.open", mock_open)

    # Make request
    response = client.get(f"/api/v1/train/train/{model_run_uuid}/logs")

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    # Verify response structure
    assert "logs" in data
    assert isinstance(data["logs"], dict)

    # Verify log content
    assert log_file_name in data["logs"]
    assert data["logs"][log_file_name] == log_content


def test_train_logs_endpoint_empty(client, monkeypatch):
    """
    Test the /api/v1/train/{model_run_uuid}/logs endpoint for a run with no logs directory.
    """
    # Get sample data for the test
    _, _, sample_model_run = get_test_data()
    model_run_uuid = sample_model_run["uuid"]

    # Define paths for exact matching using centralized functions
    run_paths = get_run_paths(model_run_uuid)
    logs_dir = run_paths.logs

    # Apply mock to simulate missing logs directory
    def mock_isdir(path):
        # Return False specifically for the logs directory
        return path != logs_dir

    monkeypatch.setattr("os.path.isdir", mock_isdir)

    # Make request
    response = client.get(f"/api/v1/train/train/{model_run_uuid}/logs")

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    # Verify empty logs dictionary
    assert "logs" in data
    assert isinstance(data["logs"], dict)
    assert data["logs"] == {}


def test_train_plots_endpoint_success(client, monkeypatch):
    """
    Test the /api/v1/train/{model_run_uuid}/plots endpoint for a successful response using mocks.
    """
    # Get sample data for the test
    _, _, sample_model_run = get_test_data()
    model_run_uuid = sample_model_run["uuid"]

    # Define test plot files
    plot_files = ["losses.png", "accuracy.png", "classification_metrics.png"]

    # Define paths for exact matching using centralized functions
    run_paths = get_run_paths(model_run_uuid)
    plots_dir = run_paths.plots
    plot_file_paths = [plots_dir / filename for filename in plot_files]

    # Define mock functions
    def mock_is_dir(path):
        # Only return True for the exact plots directory path
        return path == plots_dir

    def mock_iterdir(path):
        # Only return plot files for the exact plots directory
        if path == plots_dir:
            return plot_file_paths
        return []

    def mock_is_file(path):
        # Only return True for the exact plot file paths
        return path in plot_file_paths

    # Apply mocks for Path methods (no need to mock file reading for URL-based response)
    monkeypatch.setattr("pathlib.Path.is_dir", mock_is_dir)
    monkeypatch.setattr("pathlib.Path.iterdir", mock_iterdir)
    monkeypatch.setattr("pathlib.Path.is_file", mock_is_file)

    # Make request
    response = client.get(f"/api/v1/train/train/{model_run_uuid}/plots")

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    # Verify response structure
    assert "plots" in data
    assert isinstance(data["plots"], dict)

    # Verify plot URLs
    for filename in plot_files:
        assert filename in data["plots"]
        # Verify it's a valid URL pointing to the individual plot endpoint
        expected_url = f"/api/v1/train/{model_run_uuid}/plots/{filename}"
        assert data["plots"][filename] == expected_url


def test_train_plots_endpoint_empty(client, monkeypatch):
    """
    Test the /api/v1/train/{model_run_uuid}/plots endpoint for a run with no plots directory.
    """
    # Get sample data for the test
    _, _, sample_model_run = get_test_data()
    model_run_uuid = sample_model_run["uuid"]

    # Define paths for exact matching using centralized functions
    run_paths = get_run_paths(model_run_uuid)
    plots_dir = run_paths.plots

    # Apply mock to simulate missing plots directory
    def mock_is_dir(path):
        # Return False specifically for the plots directory
        return path != plots_dir

    monkeypatch.setattr("pathlib.Path.is_dir", mock_is_dir)

    # Make request
    response = client.get(f"/api/v1/train/train/{model_run_uuid}/plots")

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    # Verify empty plots dictionary
    assert "plots" in data
    assert isinstance(data["plots"], dict)
    assert data["plots"] == {}


def test_train_plots_endpoint_mixed_files(client, monkeypatch):
    """
    Test the /api/v1/train/{model_run_uuid}/plots endpoint with mixed file types and error handling.
    """
    # Get sample data for the test
    _, _, sample_model_run = get_test_data()

    # Define test files - mix of valid plots and other files
    all_files = [
        ("losses.png", ".png", True),  # Valid plot file
        ("accuracy.jpg", ".jpg", True),  # Valid plot file (different format)
        ("config.json", ".json", False),  # Non-plot file (should be ignored)
        ("timing.svg", ".svg", True),  # Valid plot file (SVG)
        (
            "corrupted.png",
            ".png",
            True,
        ),  # Corrupted file (would cause read error, but we don't read files anymore)
    ]

    # Define paths for exact matching using centralized functions
    run_paths = get_run_paths(sample_model_run["uuid"])
    plots_dir = run_paths.plots
    all_file_paths = [plots_dir / filename for filename, _, _ in all_files]

    # Define mock functions
    def mock_is_dir(path):
        return path == plots_dir

    def mock_iterdir(path):
        if path == plots_dir:
            return all_file_paths
        return []

    def mock_is_file(path):
        return path in all_file_paths

    # Apply mocks for Path methods (no need to mock file reading for URL-based response)
    monkeypatch.setattr("pathlib.Path.is_dir", mock_is_dir)
    monkeypatch.setattr("pathlib.Path.iterdir", mock_iterdir)
    monkeypatch.setattr("pathlib.Path.is_file", mock_is_file)

    # Make request
    response = client.get(f"/api/v1/train/train/{sample_model_run["uuid"]}/plots")

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    # Verify response structure
    assert "plots" in data
    assert isinstance(data["plots"], dict)

    # Verify only valid plot files are included
    # (corrupted.png is now included since we don't read files)
    expected_plot_files = ["losses.png", "accuracy.jpg", "timing.svg", "corrupted.png"]
    assert len(data["plots"]) == len(expected_plot_files)

    for filename in expected_plot_files:
        assert filename in data["plots"]
        # Verify it's a valid URL pointing to the individual plot endpoint
        expected_url = f"/api/v1/train/{sample_model_run["uuid"]}/plots/{filename}"
        assert data["plots"][filename] == expected_url

    # Verify non-plot files are not included
    # (corrupted.png is now included since we don't read files)
    assert "config.json" not in data["plots"]


def test_train_plots_endpoint_comprehensive_plot_types(client, monkeypatch):
    """
    Test the /api/v1/train/{model_run_uuid}/plots endpoint with comprehensive plot types
    including test samples and augmentation plots.
    """
    # Get sample data for the test
    _, _, sample_model_run = get_test_data()
    model_run_uuid = sample_model_run["uuid"]

    # Define comprehensive set of plot files including test samples and augmentations
    comprehensive_plot_files = [
        # Training metrics plots
        "losses.png",
        "accuracy.png",
        "classification_metrics.png",
        "resource_usage.png",
        "timing.png",
        # Test sample and augmentation plots
        "test_images.png",
        "test_images_augmentation_comparison.png",
        # Feature map plots
        "feature_maps.png",
        "feature_map_correlations.png",
    ]

    # Define paths for exact matching using centralized functions
    run_paths = get_run_paths(model_run_uuid)
    plots_dir = run_paths.plots
    plot_file_paths = [plots_dir / filename for filename in comprehensive_plot_files]

    # Define mock functions
    def mock_is_dir(path):
        return path == plots_dir

    def mock_iterdir(path):
        if path == plots_dir:
            return plot_file_paths
        return []

    def mock_is_file(path):
        return path in plot_file_paths

    # Apply mocks for Path methods (no need to mock file reading for URL-based response)
    monkeypatch.setattr("pathlib.Path.is_dir", mock_is_dir)
    monkeypatch.setattr("pathlib.Path.iterdir", mock_iterdir)
    monkeypatch.setattr("pathlib.Path.is_file", mock_is_file)

    # Make request
    response = client.get(f"/api/v1/train/train/{model_run_uuid}/plots")

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    # Verify response structure
    assert "plots" in data
    assert isinstance(data["plots"], dict)

    # Verify all comprehensive plot types are included
    assert len(data["plots"]) == len(comprehensive_plot_files)

    for filename in comprehensive_plot_files:
        assert (
            filename in data["plots"]
        ), f"Expected plot {filename} not found in response"
        # Verify it's a valid URL pointing to the individual plot endpoint
        expected_url = f"/api/v1/train/{model_run_uuid}/plots/{filename}"
        assert data["plots"][filename] == expected_url

    # Verify specific test sample and augmentation plots are present
    assert "test_images.png" in data["plots"]
    assert "test_images_augmentation_comparison.png" in data["plots"]
    assert "feature_maps.png" in data["plots"]
    assert "feature_map_correlations.png" in data["plots"]


def test_train_plot_image_endpoint_success(client, monkeypatch, tmp_path):
    """
    Test the /api/v1/train/{model_run_uuid}/plots/{filename} endpoint
    for serving individual plot images.
    """
    # Get sample data for the test
    _, _, sample_model_run = get_test_data()
    model_run_uuid = sample_model_run["uuid"]
    filename = "losses.png"

    # Create a temporary plot file
    temp_plots_dir = tmp_path / "plots"
    temp_plots_dir.mkdir()
    temp_plot_file = temp_plots_dir / filename

    # Create a minimal PNG file
    png_data = b"\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x0f\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82"  # pylint: disable=line-too-long
    temp_plot_file.write_bytes(png_data)

    # Mock get_run_paths to return our temporary directory
    def mock_get_run_paths(_):
        mock_paths = type("MockRunPaths", (), {"plots": temp_plots_dir})()
        return mock_paths

    monkeypatch.setattr("src.api.routes.train_stats.get_run_paths", mock_get_run_paths)

    # Make request
    response = client.get(f"/api/v1/train/train/{model_run_uuid}/plots/{filename}")

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    # Verify it's a file response (FastAPI FileResponse)
    assert response.headers["content-type"] == "image/png"


def test_train_plot_image_endpoint_not_found(client, monkeypatch):
    """
    Test the /api/v1/train/{model_run_uuid}/plots/{filename}
    endpoint for missing plot file.
    """
    # Get sample data for the test
    _, _, sample_model_run = get_test_data()
    model_run_uuid = sample_model_run["uuid"]
    filename = "nonexistent.png"

    # Mock Path methods to simulate missing file
    def mock_exists(_):
        return False

    def mock_is_file(_):
        return False

    # Apply mocks
    monkeypatch.setattr("pathlib.Path.exists", mock_exists)
    monkeypatch.setattr("pathlib.Path.is_file", mock_is_file)

    # Make request
    response = client.get(f"/api/v1/train/train/{model_run_uuid}/plots/{filename}")

    # Verify response
    assert response.status_code == status.HTTP_404_NOT_FOUND
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"].lower()
    assert filename in data["detail"]


def test_train_plot_image_endpoint_invalid_extension(client):
    """
    Test the /api/v1/train/{model_run_uuid}/plots/{filename} endpoint
    with invalid file extension.
    """
    # Get sample data for the test
    _, _, sample_model_run = get_test_data()
    model_run_uuid = sample_model_run["uuid"]
    filename = "config.txt"  # Invalid extension

    # Make request with a file that has invalid extension
    response = client.get(f"/api/v1/train/train/{model_run_uuid}/plots/{filename}")

    # This should return 404 since the file doesn't exist in our test setup
    # In a real scenario with the file existing, it would return 400 for wrong extension
    assert response.status_code == status.HTTP_404_NOT_FOUND
